package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AdurKeyDurationDiscountCurveDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AdurKeyDurationDiscountCurveQuery;
import com.xl.alm.app.service.AdurKeyDurationDiscountCurveService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/key/duration/discount/curve")
public class AdurKeyDurationDiscountCurveController extends BaseController {

    @Autowired
    private AdurKeyDurationDiscountCurveService adurKeyDurationDiscountCurveService;

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery) {
        startPage();
        List<AdurKeyDurationDiscountCurveDTO> list = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoList(adurKeyDurationDiscountCurveQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR关键久期折现曲线表含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:export')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery) {
        List<AdurKeyDurationDiscountCurveDTO> list = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoList(adurKeyDurationDiscountCurveQuery);
        ExcelUtil<AdurKeyDurationDiscountCurveDTO> util = new ExcelUtil<AdurKeyDurationDiscountCurveDTO>(AdurKeyDurationDiscountCurveDTO.class);
        util.exportExcel(list, "ADUR关键久期折现曲线表含价差数据", response);
    }

    /**
     * 获取ADUR关键久期折现曲线表含价差详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoById(id));
    }

    /**
     * 新增ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:add')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO) {
        adurKeyDurationDiscountCurveDTO.setCreateBy(getUsername());
        return toAjax(adurKeyDurationDiscountCurveService.insertAdurKeyDurationDiscountCurveDto(adurKeyDurationDiscountCurveDTO));
    }

    /**
     * 修改ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:edit')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO) {
        adurKeyDurationDiscountCurveDTO.setUpdateBy(getUsername());
        return toAjax(adurKeyDurationDiscountCurveService.updateAdurKeyDurationDiscountCurveDto(adurKeyDurationDiscountCurveDTO));
    }

    /**
     * 删除ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:remove')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurKeyDurationDiscountCurveService.deleteAdurKeyDurationDiscountCurveDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:remove')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurKeyDurationDiscountCurveService.deleteAdurKeyDurationDiscountCurveDtoByAccountPeriod(accountPeriod));
    }



    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AdurKeyDurationDiscountCurveDTO dto = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getKeyDurationCurveWithSpreadSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:edit')")
    @Log(title = "关键久期折现曲线期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        AdurKeyDurationDiscountCurveDTO dto = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setKeyDurationCurveWithSpreadSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(adurKeyDurationDiscountCurveService.updateAdurKeyDurationDiscountCurveDto(dto));
    }
}
