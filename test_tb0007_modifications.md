# TB0007 月度折现因子表含价差修改测试

## 修改内容总结

### 1. 移除导入功能
- ✅ 移除了Controller中的导入相关方法：
  - `importTemplate()` - 导入模板下载方法
  - `importData()` - 导入数据方法
- ✅ 移除了Service接口中的导入方法：
  - `importAdurMonthlyDiscountFactorWithSpreadDto()`
- ✅ 移除了Service实现类中的导入方法实现
- ✅ 移除了不再使用的import语句：
  - `import org.springframework.web.multipart.MultipartFile;`

### 2. 添加价差类型列显示
- ✅ 在前端表格中添加了价差类型列：
```vue
<el-table-column label="价差类型" align="center" prop="spreadType">
  <template slot-scope="scope">
    <dict-tag :options="dict.type.adur_spread_type" :value="scope.row.spreadType"/>
  </template>
</el-table-column>
```

### 3. 完善导出Excel字典翻译
- ✅ 确认Controller中的`convertDictValueToLabel()`方法包含所有字典字段转换：
  - 久期类型 (adur_duration_type)
  - 基点类型 (adur_basis_point_type)
  - 日期类型 (adur_date_type)
  - 价差类型 (adur_spread_type)
  - 账户名称 (adur_account_name)
  - 曲线细分类 (adur_curve_sub_category) - 新增

## 测试验证点

### 前端测试
1. 访问TB0007页面，确认：
   - 表格中显示价差类型列
   - 价差类型列正确显示字典翻译
   - 没有导入按钮
   - 导出按钮正常工作

### 后端测试
1. 确认Controller编译无错误
2. 确认Service接口和实现类编译无错误
3. 测试导出功能，验证Excel中字典值已转换为中文标签

### API测试
1. 测试导出接口：`POST /adur/monthly/discount/factor/with/spread/export`
2. 确认导入相关接口已移除：
   - `/adur/monthly/discount/factor/with/spread/importTemplate` (应返回404)
   - `/adur/monthly/discount/factor/with/spread/importData` (应返回404)

## 字典配置确认
确认以下字典类型在系统中已配置：
- adur_duration_type (久期类型)
- adur_basis_point_type (基点类型)
- adur_date_type (日期类型)
- adur_spread_type (价差类型)
- adur_account_name (账户名称)
- adur_curve_sub_category (曲线细分类)

## 修改文件清单
1. `web/src/views/adur/monthly/discount/factor/with/spread/index.vue` - 添加价差类型列
2. `app/src/main/java/com/xl/alm/app/controller/AdurMonthlyDiscountFactorWithSpreadController.java` - 移除导入方法，完善字典转换
3. `app/src/main/java/com/xl/alm/app/service/AdurMonthlyDiscountFactorWithSpreadService.java` - 移除导入方法接口
4. `app/src/main/java/com/xl/alm/app/service/impl/AdurMonthlyDiscountFactorWithSpreadServiceImpl.java` - 移除导入方法实现
